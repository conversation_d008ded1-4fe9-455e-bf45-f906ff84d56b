# 测试模板修复的PowerShell脚本

Write-Host "=== 测试模板修复 ===" -ForegroundColor Green
Write-Host ""

# 检查项目是否存在
$debugProject = "MassStorageStableTestTool.Reports\Debug\MassStorageStableTestTool.Reports.Debug.csproj"
if (-not (Test-Path $debugProject)) {
    Write-Host "错误: 找不到调试项目文件" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "1. 构建调试项目..." -ForegroundColor Yellow
    dotnet build $debugProject --configuration Debug
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "构建失败!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "构建成功!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "2. 运行模板调试器..." -ForegroundColor Yellow
    dotnet run --project $debugProject
    
    Write-Host ""
    Write-Host "3. 检查生成的调试报告文件..." -ForegroundColor Yellow
    $reportFiles = Get-ChildItem -Path "." -Name "debug_report_*.txt" | Sort-Object LastWriteTime -Descending
    
    if ($reportFiles.Count -gt 0) {
        $latestReport = $reportFiles[0]
        Write-Host "找到最新的调试报告: $latestReport" -ForegroundColor Green
        
        $content = Get-Content $latestReport -Raw
        Write-Host "报告内容长度: $($content.Length) 字符" -ForegroundColor Green
        
        if ($content.Length -gt 0) {
            Write-Host "报告生成成功!" -ForegroundColor Green
        } else {
            Write-Host "警告: 报告文件为空" -ForegroundColor Yellow
        }
    } else {
        Write-Host "未找到调试报告文件" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "执行过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
