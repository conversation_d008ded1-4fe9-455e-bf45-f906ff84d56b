using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Common;
using MassStorageStableTestTool.Reports.Models;
using Microsoft.Extensions.Logging;
using Scriban;
using System.Text;

namespace MassStorageStableTestTool.Reports.Generators;

/// <summary>
/// 文本格式报告生成器
/// </summary>
public class TextReportGenerator : BaseReportGenerator
{
    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public override ReportFormat SupportedFormat => ReportFormat.Text;

    /// <summary>
    /// 生成器名称
    /// </summary>
    public override string GeneratorName => "Text Report Generator";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public TextReportGenerator(ILogger<TextReportGenerator> logger, ReportGenerationConfiguration? configuration = null)
        : base(logger, configuration)
    {
    }

    /// <summary>
    /// 生成报告内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected override async Task<string> GenerateReportContentAsync(object reportData, string template)
    {
        try
        {
            // 1. 验证输入参数
            if (string.IsNullOrWhiteSpace(template))
            {
                _logger.LogWarning("模板内容为空，使用简单文本报告");
                return GenerateSimpleTextReport(reportData);
            }

            if (reportData == null)
            {
                _logger.LogWarning("报告数据为空，使用简单文本报告");
                return GenerateSimpleTextReport(reportData);
            }

            _logger.LogDebug("开始解析模板，模板长度: {TemplateLength}", template.Length);
            _logger.LogDebug("报告数据类型: {DataType}", reportData.GetType().Name);

            // 2. 尝试解析模板
            Template scriptTemplate;
            try
            {
                scriptTemplate = Template.Parse(template);
                _logger.LogDebug("模板解析成功");
            }
            catch (Exception parseEx)
            {
                _logger.LogError(parseEx, "模板解析失败: {ErrorMessage}", parseEx.Message);

                // 尝试使用简化的模板
                _logger.LogInformation("尝试使用简化模板");
                return await TryWithSimplifiedTemplate(reportData);
            }

            // 3. 验证模板解析结果
            if (scriptTemplate == null)
            {
                _logger.LogError("模板解析结果为null");
                return GenerateSimpleTextReport(reportData);
            }

            if (scriptTemplate.HasErrors)
            {
                _logger.LogError("模板包含错误: {Errors}",
                    string.Join(", ", scriptTemplate.Messages.Select(m => m.ToString())));
                return await TryWithSimplifiedTemplate(reportData);
            }

            // 4. 验证和准备数据
            _logger.LogDebug("验证报告数据结构");
            var validatedData = ValidateAndPrepareReportData(reportData);

            // 5. 尝试渲染模板
            _logger.LogDebug("开始渲染模板");
            try
            {
                var result = await scriptTemplate.RenderAsync(validatedData);
                _logger.LogDebug("模板渲染成功，结果长度: {ResultLength}", result?.Length ?? 0);

                if (string.IsNullOrWhiteSpace(result))
                {
                    _logger.LogWarning("模板渲染结果为空");
                    return GenerateSimpleTextReport(reportData);
                }

                return result;
            }
            catch (Exception renderEx)
            {
                _logger.LogError(renderEx, "模板渲染失败: {ErrorMessage}", renderEx.Message);

                // 记录详细的数据结构信息
                LogReportDataStructure(reportData);
                LogReportDataStructure(validatedData, "验证后的数据");

                // 尝试逐步调试模板问题
                return await DiagnoseTemplateIssue(scriptTemplate, validatedData, reportData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成文本报告时发生未预期的错误: {ErrorMessage}", ex.Message);
            return GenerateSimpleTextReport(reportData);
        }
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public override string GetDefaultFileExtension() => ".txt";

    /// <summary>
    /// 获取后备模板
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected override string GetFallbackTemplate()
    {
        return @"
================================================================================
                           {{ Configuration?.Title ?? '磁盘稳定性测试报告' }}
================================================================================

报告生成时间: {{ GeneratedAt | date: 'yyyy-MM-dd HH:mm:ss' }}
生成器版本: {{ Version }}
报告作者: {{ Configuration?.Author ?? '系统' }}
{{ if Configuration?.Organization }}组织: {{ Configuration.Organization }}{{ end }}

================================================================================
                                测试概览
================================================================================

测试开始时间: {{ TestSuite?.StartTime | date: 'yyyy-MM-dd HH:mm:ss' }}
测试结束时间: {{ TestSuite?.EndTime | date: 'yyyy-MM-dd HH:mm:ss' }}
测试总耗时: {{ TestSuite?.Duration.TotalMinutes | round: 2 }} 分钟

目标驱动器: {{ TestSuite?.Configuration?.TargetDrive ?? '未知' }}
测试工具数量: {{ TestSuite?.Configuration?.SelectedTools.Count ?? 0 }}
选中的工具: {{ TestSuite?.Configuration?.SelectedTools | join: ', ' }}

================================================================================
                                测试结果摘要
================================================================================

总测试数: {{ TestSuite?.TotalTestsCount ?? 0 }}
成功测试数: {{ TestSuite?.SuccessfulTestsCount ?? 0 }}
失败测试数: {{ TestSuite?.FailedTestsCount ?? 0 }}
成功率: {{ TestSuite?.SuccessRate | round: 1 }}%

整体状态: {{ TestSuite?.Status ?? '未知' }}
{{ if TestSuite?.AllTestsPassed }}✅ 所有测试均通过{{ else }}❌ 存在测试失败{{ end }}

================================================================================
                                详细测试结果
================================================================================

{{ for test_result in TestSuite?.TestResults }}
工具名称: {{ test_result?.ToolName ?? '未知' }}
测试状态: {{ test_result?.Status ?? '未知' }}
测试结果: {{ if test_result?.Success }}✅ 成功{{ else }}❌ 失败{{ end }}
开始时间: {{ test_result?.StartTime | date: 'yyyy-MM-dd HH:mm:ss' }}
结束时间: {{ test_result?.EndTime | date: 'yyyy-MM-dd HH:mm:ss' }}
耗时: {{ test_result?.Duration.TotalMinutes | round: 2 }} 分钟

{{ if test_result?.Performance }}
性能数据:
{{ if test_result.Performance?.ReadSpeed }}  读取速度: {{ test_result.Performance.ReadSpeed | round: 2 }} MB/s{{ end }}
{{ if test_result.Performance?.WriteSpeed }}  写入速度: {{ test_result.Performance.WriteSpeed | round: 2 }} MB/s{{ end }}
{{ if test_result.Performance?.ReadIOPS }}  读取IOPS: {{ test_result.Performance.ReadIOPS | round: 0 }}{{ end }}
{{ if test_result.Performance?.WriteIOPS }}  写入IOPS: {{ test_result.Performance.WriteIOPS | round: 0 }}{{ end }}
{{ if test_result.Performance?.ReadLatency }}  读取延迟: {{ test_result.Performance.ReadLatency | round: 2 }} ms{{ end }}
{{ if test_result.Performance?.WriteLatency }}  写入延迟: {{ test_result.Performance.WriteLatency | round: 2 }} ms{{ end }}
{{ end }}

{{ if test_result?.ErrorMessage }}
错误信息: {{ test_result.ErrorMessage }}
{{ end }}

{{ if test_result?.Warnings?.Count > 0 }}
警告信息:
{{ for warning in test_result.Warnings }}  - {{ warning }}
{{ end }}
{{ end }}

{{ if test_result?.OutputFiles?.Count > 0 }}
输出文件:
{{ for file in test_result.OutputFiles }}  - {{ file }}
{{ end }}
{{ end }}

--------------------------------------------------------------------------------
{{ end }}

{{ if Configuration?.IncludeSystemInfo and TestSuite?.SystemInfo }}
================================================================================
                                系统信息
================================================================================

操作系统: {{ TestSuite.SystemInfo?.OperatingSystem ?? '未知' }}
处理器: {{ TestSuite.SystemInfo?.Processor ?? '未知' }}
系统架构: {{ TestSuite.SystemInfo?.Architecture ?? '未知' }}
机器名称: {{ TestSuite.SystemInfo?.ComputerName ?? '未知' }}
用户名称: {{ TestSuite.SystemInfo?.UserName ?? '未知' }}
总内存: {{ TestSuite.SystemInfo?.TotalMemory | round: 2 }} GB
可用内存: {{ TestSuite.SystemInfo?.AvailableMemory | round: 2 }} GB
.NET版本: {{ TestSuite.SystemInfo?.DotNetVersion ?? '未知' }}
{{ end }}

{{ if TestSuite?.DriveInfo }}
================================================================================
                                驱动器信息
================================================================================

驱动器: {{ TestSuite.DriveInfo?.Name ?? '未知' }}
标签: {{ TestSuite.DriveInfo?.Label ?? '未知' }}
文件系统: {{ TestSuite.DriveInfo?.FileSystem ?? '未知' }}
驱动器类型: {{ TestSuite.DriveInfo?.DriveType ?? '未知' }}
总容量: {{ TestSuite.DriveInfo?.TotalSize / (1024.0 * 1024.0 * 1024.0) | round: 2 }} GB
可用空间: {{ TestSuite.DriveInfo?.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0) | round: 2 }} GB
已用空间: {{ (TestSuite.DriveInfo?.TotalSize - TestSuite.DriveInfo?.AvailableFreeSpace) / (1024.0 * 1024.0 * 1024.0) | round: 2 }} GB
使用率: {{ (TestSuite.DriveInfo?.TotalSize - TestSuite.DriveInfo?.AvailableFreeSpace) / TestSuite.DriveInfo?.TotalSize * 100 | round: 1 }}%
驱动器状态: {{ if TestSuite.DriveInfo?.IsReady }}就绪{{ else }}未就绪{{ end }}
{{ end }}

{{ if Configuration?.IncludeDetailedLogs and TestSuite?.Logs?.Count > 0 }}
================================================================================
                                详细日志
================================================================================

{{ for log in TestSuite.Logs }}{{ log }}
{{ end }}
{{ end }}

{{ if TestSuite?.Warnings?.Count > 0 }}
================================================================================
                                套件警告
================================================================================

{{ for warning in TestSuite.Warnings }}- {{ warning }}
{{ end }}
{{ end }}

================================================================================
                                报告结束
================================================================================

报告生成完成时间: {{ GeneratedAt | date: 'yyyy-MM-dd HH:mm:ss' }}
生成器: {{ GeneratedBy }}
版本: {{ Version }}
";
    }

    /// <summary>
    /// 尝试使用简化模板
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>报告内容</returns>
    private async Task<string> TryWithSimplifiedTemplate(object? reportData)
    {
        try
        {
            _logger.LogInformation("尝试使用简化模板生成报告");

            // 使用最基本的模板，避免复杂的语法
            var simpleTemplate = @"
================================================================================
                           {{ Configuration.Title ?? '磁盘稳定性测试报告' }}
================================================================================

报告生成时间: {{ GeneratedAt }}
生成器: {{ GeneratedBy }}

{{ if TestSuite }}
测试开始时间: {{ TestSuite.StartTime }}
测试结束时间: {{ TestSuite.EndTime }}
测试状态: {{ TestSuite.Status }}
{{ end }}

================================================================================
                                报告结束
================================================================================
";

            var scriptTemplate = Template.Parse(simpleTemplate);
            if (scriptTemplate.HasErrors)
            {
                _logger.LogWarning("简化模板也解析失败，使用最基本的文本报告");
                return GenerateSimpleTextReport(reportData);
            }

            var result = await scriptTemplate.RenderAsync(reportData ?? new object());
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "简化模板渲染也失败: {ErrorMessage}", ex.Message);
            return GenerateSimpleTextReport(reportData);
        }
    }



    /// <summary>
    /// 生成简单的文本报告（当模板解析失败时使用）
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>简单文本报告</returns>
    private string GenerateSimpleTextReport(object? reportData)
    {
        var sb = new StringBuilder();

        sb.AppendLine("================================================================================");
        sb.AppendLine("                           磁盘稳定性测试报告");
        sb.AppendLine("================================================================================");
        sb.AppendLine();
        sb.AppendLine($"报告生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"生成器: {GeneratorName}");
        sb.AppendLine();
        sb.AppendLine("注意: 由于模板解析失败，此报告使用简化格式生成。");
        sb.AppendLine();

        // 尝试提取基本信息
        if (reportData != null)
        {
            try
            {
                var dataType = reportData.GetType();
                var testSuiteProp = dataType.GetProperty("TestSuite");
                if (testSuiteProp != null)
                {
                    var testSuite = testSuiteProp.GetValue(reportData);
                    if (testSuite != null)
                    {
                        sb.AppendLine("测试套件信息:");
                        var testSuiteType = testSuite.GetType();

                        // 尝试获取基本属性
                        TryAddProperty(sb, testSuite, testSuiteType, "StartTime", "开始时间");
                        TryAddProperty(sb, testSuite, testSuiteType, "EndTime", "结束时间");
                        TryAddProperty(sb, testSuite, testSuiteType, "Status", "状态");
                        TryAddProperty(sb, testSuite, testSuiteType, "TotalTestsCount", "总测试数");
                        TryAddProperty(sb, testSuite, testSuiteType, "SuccessfulTestsCount", "成功测试数");
                        TryAddProperty(sb, testSuite, testSuiteType, "FailedTestsCount", "失败测试数");
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine($"提取测试信息时出错: {ex.Message}");
            }
        }

        sb.AppendLine();
        sb.AppendLine("================================================================================");

        return sb.ToString();
    }

    /// <summary>
    /// 验证和准备报告数据
    /// </summary>
    /// <param name="reportData">原始报告数据</param>
    /// <returns>验证后的报告数据</returns>
    private object ValidateAndPrepareReportData(object reportData)
    {
        try
        {
            if (reportData == null)
            {
                _logger.LogWarning("报告数据为null，创建默认数据结构");
                return CreateDefaultReportData();
            }

            var dataType = reportData.GetType();
            _logger.LogDebug("验证报告数据类型: {DataType}", dataType.Name);

            // 检查必要的属性
            var configProp = dataType.GetProperty("Configuration");
            var testSuiteProp = dataType.GetProperty("TestSuite");
            var generatedAtProp = dataType.GetProperty("GeneratedAt");
            var generatedByProp = dataType.GetProperty("GeneratedBy");
            var versionProp = dataType.GetProperty("Version");

            var hasIssues = false;
            var issues = new List<string>();

            // 验证Configuration属性
            if (configProp == null)
            {
                issues.Add("缺少Configuration属性");
                hasIssues = true;
            }
            else
            {
                var configValue = configProp.GetValue(reportData);
                if (configValue == null)
                {
                    issues.Add("Configuration属性为null");
                    hasIssues = true;
                }
                else
                {
                    // 验证Configuration的Title属性
                    var configType = configValue.GetType();
                    var titleProp = configType.GetProperty("Title");
                    if (titleProp == null)
                    {
                        issues.Add("Configuration缺少Title属性");
                        hasIssues = true;
                    }
                    else
                    {
                        var titleValue = titleProp.GetValue(configValue);
                        _logger.LogDebug("Configuration.Title值: {Title}", titleValue ?? "null");
                    }
                }
            }

            // 验证TestSuite属性
            if (testSuiteProp == null)
            {
                issues.Add("缺少TestSuite属性");
                hasIssues = true;
            }
            else
            {
                var testSuiteValue = testSuiteProp.GetValue(reportData);
                if (testSuiteValue == null)
                {
                    issues.Add("TestSuite属性为null");
                    hasIssues = true;
                }
            }

            if (hasIssues)
            {
                _logger.LogWarning("报告数据验证发现问题: {Issues}", string.Join(", ", issues));
                return CreateSafeReportData(reportData);
            }

            _logger.LogDebug("报告数据验证通过");
            return reportData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证报告数据时出错: {ErrorMessage}", ex.Message);
            return CreateDefaultReportData();
        }
    }

    /// <summary>
    /// 创建默认报告数据
    /// </summary>
    /// <returns>默认报告数据</returns>
    private object CreateDefaultReportData()
    {
        return new
        {
            Configuration = new
            {
                Title = "磁盘稳定性测试报告",
                Author = "系统",
                Organization = "",
                IncludeSystemInfo = true,
                IncludeDetailedLogs = false
            },
            TestSuite = new
            {
                StartTime = DateTime.Now.AddHours(-1),
                EndTime = DateTime.Now,
                Duration = TimeSpan.FromHours(1),
                Status = "未知",
                AllTestsPassed = false,
                TotalTestsCount = 0,
                SuccessfulTestsCount = 0,
                FailedTestsCount = 0,
                SuccessRate = 0.0,
                TestResults = new object[0],
                Warnings = new string[0],
                Logs = new string[0],
                SystemInfo = (object?)null,
                DriveInfo = (object?)null,
                Configuration = new
                {
                    TargetDrive = "未知",
                    SelectedTools = new string[0]
                }
            },
            GeneratedAt = DateTime.Now,
            GeneratedBy = GeneratorName,
            Version = "1.0.0"
        };
    }

    /// <summary>
    /// 创建安全的报告数据（基于原始数据但确保关键属性不为null）
    /// </summary>
    /// <param name="originalData">原始数据</param>
    /// <returns>安全的报告数据</returns>
    private object CreateSafeReportData(object originalData)
    {
        try
        {
            var dataType = originalData.GetType();
            var properties = dataType.GetProperties();
            var safeData = new Dictionary<string, object?>();

            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(originalData);
                    safeData[prop.Name] = value;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("无法获取属性 {PropertyName}: {Error}", prop.Name, ex.Message);
                    safeData[prop.Name] = null;
                }
            }

            // 确保Configuration不为null
            if (!safeData.ContainsKey("Configuration") || safeData["Configuration"] == null)
            {
                safeData["Configuration"] = new
                {
                    Title = "磁盘稳定性测试报告",
                    Author = "系统",
                    Organization = "",
                    IncludeSystemInfo = true,
                    IncludeDetailedLogs = false
                };
            }

            // 确保其他关键属性
            safeData["GeneratedAt"] = safeData.GetValueOrDefault("GeneratedAt", DateTime.Now);
            safeData["GeneratedBy"] = safeData.GetValueOrDefault("GeneratedBy", GeneratorName);
            safeData["Version"] = safeData.GetValueOrDefault("Version", "1.0.0");

            return safeData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建安全报告数据时出错: {ErrorMessage}", ex.Message);
            return CreateDefaultReportData();
        }
    }

    /// <summary>
    /// 诊断模板问题
    /// </summary>
    /// <param name="template">已解析的模板</param>
    /// <param name="validatedData">验证后的数据</param>
    /// <param name="originalData">原始数据</param>
    /// <returns>报告内容</returns>
    private async Task<string> DiagnoseTemplateIssue(Template template, object validatedData, object originalData)
    {
        try
        {
            _logger.LogInformation("开始诊断模板问题");

            // 尝试使用最简单的模板测试数据绑定
            var testTemplate = Template.Parse("测试: {{ Configuration.Title ?? '默认标题' }}");
            if (testTemplate.HasErrors)
            {
                _logger.LogError("连简单的测试模板都解析失败");
                return GenerateSimpleTextReport(originalData);
            }

            try
            {
                var testResult = await testTemplate.RenderAsync(validatedData);
                _logger.LogDebug("简单模板测试结果: {Result}", testResult);
            }
            catch (Exception testEx)
            {
                _logger.LogError(testEx, "简单模板测试失败: {ErrorMessage}", testEx.Message);
                return GenerateSimpleTextReport(originalData);
            }

            // 如果简单模板成功，尝试使用更安全的模板
            return await TryWithSimplifiedTemplate(validatedData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "诊断模板问题时出错: {ErrorMessage}", ex.Message);
            return GenerateSimpleTextReport(originalData);
        }
    }

    /// <summary>
    /// 记录报告数据结构信息（用于调试）
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="dataLabel">数据标签</param>
    private void LogReportDataStructure(object? reportData, string dataLabel = "报告数据")
    {
        try
        {
            if (reportData == null)
            {
                _logger.LogDebug("{DataLabel}为null", dataLabel);
                return;
            }

            var dataType = reportData.GetType();
            _logger.LogDebug("{DataLabel}类型: {DataType}", dataLabel, dataType.FullName);

            // 记录属性信息
            var properties = dataType.GetProperties();
            _logger.LogDebug("{DataLabel}包含 {PropertyCount} 个属性:", dataLabel, properties.Length);

            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(reportData);
                    var valueInfo = value?.ToString() ?? "null";
                    if (valueInfo.Length > 100)
                    {
                        valueInfo = valueInfo[..100] + "...";
                    }
                    _logger.LogDebug("  - {PropertyName} ({PropertyType}): {Value}",
                        prop.Name, prop.PropertyType.Name, valueInfo);
                }
                catch (Exception propEx)
                {
                    _logger.LogDebug("  - {PropertyName}: 无法获取值 - {Error}",
                        prop.Name, propEx.Message);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录{DataLabel}结构信息时出错: {ErrorMessage}", dataLabel, ex.Message);
        }
    }

    /// <summary>
    /// 尝试添加属性信息到字符串构建器
    /// </summary>
    private static void TryAddProperty(StringBuilder sb, object obj, Type type, string propertyName, string displayName)
    {
        try
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null)
            {
                var value = prop.GetValue(obj);
                sb.AppendLine($"{displayName}: {value}");
            }
        }
        catch (Exception ex)
        {
            sb.AppendLine($"{displayName}: 获取失败 - {ex.Message}");
        }
    }
}
